"use client";

import React from "react";
import DashboardHeader from "@/components/DashboardHeader";
import { useLibrary } from "../components/LibraryLayoutClient/context";

const MyDraftsPage = () => {
  const { selectedFolder } = useLibrary();

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="My Drafts"
        description="View and manage your draft documents"
        actions={[
          {
            label: "Create Draft",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Create Draft"),
          },
        ]}
      />

      <div className="flex-1 p-6 bg-neutral-50">
        <div className="text-center py-12">
          <p className="text-lg font-medium text-neutral-700">
            {selectedFolder ? `Drafts in ${selectedFolder}` : "My Drafts"}
          </p>
          <p className="text-sm text-neutral-500 mt-2">
            Your draft documents will appear here
          </p>
        </div>
      </div>
    </div>
  );
};

export default MyDraftsPage;
