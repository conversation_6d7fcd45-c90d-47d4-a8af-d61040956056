"use client";

import React, { useEffect } from "react";
import DashboardHeader from "@/components/DashboardHeader";
import { sampleFileTree } from "../documents/components/FolderSidebar/data";
import { TreeNodeI } from "../documents/components/FolderSidebar/types";
import { useLibrary } from "../components/LibraryLayoutClient/context";

const FileTreePage = () => {
  const { fileTree, setFileTree, selectedItem } = useLibrary();

  // Initialize with sample data
  useEffect(() => {
    setFileTree(sampleFileTree);
  }, [setFileTree]);

  const selectedNode = selectedItem
    ? findNodeInTree(fileTree, selectedItem)
    : null;

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="File Tree Demo"
        description="Comprehensive file tree with drag & drop, inline editing, and more"
        actions={[
          {
            label: "Reset Demo",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => setFileTree(sampleFileTree),
          },
        ]}
      />

      <div className="flex-1 p-6 bg-neutral-50">
        <div className="bg-white rounded-lg border border-neutral-200 p-6">
          <h2 className="text-lg font-semibold mb-4">File Tree Demo</h2>

          {selectedNode ? (
            <div className="space-y-4">
              <h3 className="text-md font-medium">Selected Item</h3>
              <div className="bg-neutral-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span>{" "}
                    {selectedNode.name}
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>{" "}
                    {selectedNode.type}
                  </div>
                  <div>
                    <span className="font-medium">Path:</span>{" "}
                    {selectedNode.path}
                  </div>
                  {selectedNode.type === "file" && (
                    <>
                      <div>
                        <span className="font-medium">Extension:</span>{" "}
                        {selectedNode.extension}
                      </div>
                      <div>
                        <span className="font-medium">Size:</span>{" "}
                        {formatFileSize(selectedNode.size)}
                      </div>
                      <div>
                        <span className="font-medium">Modified:</span>{" "}
                        {selectedNode.lastModified.toLocaleDateString()}
                      </div>
                    </>
                  )}
                  {selectedNode.type === "folder" && (
                    <>
                      <div>
                        <span className="font-medium">Items:</span>{" "}
                        {selectedNode.count}
                      </div>
                      <div>
                        <span className="font-medium">Expanded:</span>{" "}
                        {selectedNode.isExpanded ? "Yes" : "No"}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-neutral-500">
              <p className="text-lg font-medium">No item selected</p>
              <p className="text-sm mt-2">
                Click on a file or folder in the sidebar to see its details
              </p>
            </div>
          )}

          <div className="mt-8">
            <h3 className="text-md font-medium mb-4">Features</h3>
            <ul className="space-y-2 text-sm text-neutral-600">
              <li>
                • <strong>Navigation:</strong> Overview, Drafts, and Approvals
                links with active state detection
              </li>
              <li>
                • <strong>File Tree:</strong> Hierarchical display with
                expand/collapse functionality
              </li>
              <li>
                • <strong>Drag & Drop:</strong> Move files and folders between
                locations
              </li>
              <li>
                • <strong>Inline Editing:</strong> Double-click or right-click
                to rename items
              </li>
              <li>
                • <strong>Context Menu:</strong> Right-click for rename and
                delete options
              </li>
              <li>
                • <strong>Add Items:</strong> Use the + buttons in the Library
                header to add files/folders
              </li>
              <li>
                • <strong>Performance:</strong> Optimized for large file trees
                with memoization
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper functions
function findNodeInTree(nodes: TreeNodeI[], id: string): TreeNodeI | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.type === "folder" && node.children) {
      const found = findNodeInTree(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export default FileTreePage;
