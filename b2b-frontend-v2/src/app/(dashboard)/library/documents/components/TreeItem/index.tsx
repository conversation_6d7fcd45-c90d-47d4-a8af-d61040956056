import React, { useState, useRef, useCallback, useEffect } from "react";
import { cn } from "@/utils/class-merge";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  FileIcon,
  VerticalElipsisIcon,
} from "../../../../../../../public/assets/svgs/common";
import { TreeItemProps } from "../FolderSidebar/types";
import ConfirmDialog from "../../../../../../components/ConfirmDialog";
import {
  validateFileName,
  validateFolderName,
  getSiblingNodes,
} from "../FolderSidebar/utils";

const TreeItem: React.FC<TreeItemProps> = ({
  node,
  level,
  isSelected,
  isEditing,
  selectedItemId,
  editingItemId,
  onSelect,
  onToggleExpand,
  onStartEdit,
  onFinishEdit,
  onCancelEdit,
  onDelete,
  onCreateFile,
  onCreateFolder,
  enableDragDrop,
  maxDepth,
  allNodes,
  renderChildren = true, // Default to true for backward compatibility
}) => {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editValue, setEditValue] = useState(node.name);
  const [validationError, setValidationError] = useState<string>("");
  const [isHoveringMenu, setIsHoveringMenu] = useState(false);
  const [menuCoordinates, setMenuCoordinates] = useState({ top: 0, left: 0 });
  const inputRef = useRef<HTMLInputElement>(null);
  const contextMenuRef = useRef<HTMLDivElement>(null);
  const itemRef = useRef<HTMLDivElement>(null);
  const isFolder = node.type === "folder";

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showContextMenu &&
        contextMenuRef.current &&
        !contextMenuRef.current.contains(event.target as Node)
      ) {
        setShowContextMenu(false);
      }
    };

    if (showContextMenu) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [showContextMenu]);

  // Focus input when editing starts - use ref callback instead of useEffect
  const inputRefCallback = useCallback(
    (element: HTMLInputElement | null) => {
      if (element && isEditing) {
        element.focus();
        element.select();
      }
      inputRef.current = element;
    },
    [isEditing],
  );

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      onSelect(node);
    }
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isFolder) {
      onToggleExpand(node.id);
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      onStartEdit(node.id, node.type);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEditValue(value);

    if (value.trim()) {
      const siblingNodes = allNodes ? getSiblingNodes(allNodes, node.id) : [];
      const validation = isFolder
        ? validateFolderName(value.trim(), siblingNodes, node.id)
        : validateFileName(value.trim(), siblingNodes, node.id);
      setValidationError(validation.isValid ? "" : validation.error || "");
    } else {
      setValidationError("Name cannot be empty");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (!validationError && editValue.trim()) {
        onFinishEdit(node.id, editValue.trim());
        setValidationError("");
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      setEditValue(node.name);
      setValidationError("");
      onCancelEdit();
    }
  };

  const handleInputBlur = () => {
    onFinishEdit(node.id, editValue.trim());
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!showContextMenu && itemRef.current) {
      const rect = itemRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const menuHeight = 200; // Approximate menu height
      const menuWidth = 160; // min-w-40 = 160px
      const spaceBelow = viewportHeight - rect.bottom;

      // Determine vertical position
      const position = spaceBelow < menuHeight ? "top" : "bottom";

      // Calculate coordinates for fixed positioning
      let top =
        position === "bottom" ? rect.bottom + 4 : rect.top - menuHeight - 4;
      let left = rect.right - menuWidth;

      // Ensure menu doesn't go off-screen horizontally
      if (left < 0) {
        left = rect.left;
      }
      if (left + menuWidth > viewportWidth) {
        left = viewportWidth - menuWidth - 8;
      }

      // Ensure menu doesn't go off-screen vertically
      if (top < 0) {
        top = 8;
      }
      if (top + menuHeight > viewportHeight) {
        top = viewportHeight - menuHeight - 8;
      }

      setMenuCoordinates({ top, left });
    }

    setShowContextMenu(!showContextMenu);
  };

  const handleContextMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent closing when clicking inside context menu
  };

  const handleMenuMouseEnter = () => {
    setIsHoveringMenu(true);
  };

  const handleMenuMouseLeave = () => {
    setIsHoveringMenu(false);
    setTimeout(() => {
      if (!isHoveringMenu) {
        setShowContextMenu(false);
      }
    }, 100);
  };

  const getFileExtension = (fileName: string) => {
    const lastDot = fileName.lastIndexOf(".");
    return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
  };

  const indentWidth = level * 16; // 16px per level

  return (
    <div className="relative">
      <div
        ref={itemRef}
        className={cn(
          "flex items-center py-1 px-2 rounded-md cursor-pointer transition-colors group",
          "hover:bg-neutral-100",
          isSelected && "bg-green-100  text-green-700 dark:text-green-400",
        )}
        style={{ paddingLeft: `${indentWidth + 8}px` }}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        onContextMenu={handleContextMenu}
      >
        {/* Expand/Collapse Icon */}
        {isFolder && (
          <button
            onClick={handleToggleExpand}
            className="flex items-center justify-center w-4 h-4 mr-1 hover:bg-neutral-200  rounded transition-colors"
            title={node.isExpanded ? "Collapse folder" : "Expand folder"}
          >
            {node.isExpanded ? <ChevronDownIcon /> : <ChevronRightIcon />}
          </button>
        )}

        {/* File/Folder Icon */}
        {!isFolder && (
          <div className="flex items-center justify-center w-4 h-4 mr-2">
            <FileIcon extension={getFileExtension(node.name)} />
          </div>
        )}

        {/* Name Input/Display */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="w-full">
              <input
                ref={inputRefCallback}
                type="text"
                value={editValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputBlur}
                className={cn(
                  "w-full px-1 py-0 text-sm bg-white border rounded focus:outline-none",
                  validationError ? "border-red-500" : "border-green-500",
                )}
              />
              {validationError && (
                <div className="text-xs text-red-500 mt-1 px-1">
                  {validationError}
                </div>
              )}
            </div>
          ) : (
            <span className="text-sm truncate block">{node.name}</span>
          )}
        </div>

        {/* Context Menu Button */}
        {!isEditing && (
          <button
            onClick={handleContextMenu}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-neutral-200 rounded transition-all"
            aria-label="More options"
            title="More options"
          >
            <VerticalElipsisIcon />
          </button>
        )}
      </div>

      {/* Context Menu */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed bg-white border border-neutral-200 rounded-md shadow-lg z-[9999] min-w-40"
          style={{
            top: `${menuCoordinates.top}px`,
            left: `${menuCoordinates.left}px`,
          }}
          onClick={handleContextMenuClick}
          onMouseEnter={handleMenuMouseEnter}
          onMouseLeave={handleMenuMouseLeave}
        >
          {isFolder && onCreateFile && (
            <button
              onClick={() => {
                onCreateFile(node.id);
                setShowContextMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-neutral-100 transition-colors"
              title="Create a new file in this folder"
            >
              New File
            </button>
          )}
          {isFolder && onCreateFolder && (
            <button
              onClick={() => {
                onCreateFolder(node.id);
                setShowContextMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-neutral-100 transition-colors"
              title="Create a new folder in this folder"
            >
              New Folder
            </button>
          )}
          {isFolder && (onCreateFile || onCreateFolder) && (
            <div className="border-t border-neutral-200 my-1" />
          )}
          <button
            onClick={() => {
              onStartEdit(node.id, node.type);
              setShowContextMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm hover:bg-neutral-100 transition-colors"
            title={`Rename this ${isFolder ? "folder" : "file"}`}
          >
            Rename
          </button>
          <button
            onClick={() => {
              setShowDeleteDialog(true);
              setShowContextMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors"
            title={`Delete this ${isFolder ? "folder" : "file"} permanently`}
          >
            Delete
          </button>
        </div>
      )}

      {/* Children */}
      {renderChildren && isFolder && node.isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <TreeItem
              key={child.id}
              node={child}
              level={level + 1}
              isSelected={selectedItemId ? selectedItemId === child.id : false}
              isEditing={editingItemId ? editingItemId === child.id : false}
              selectedItemId={selectedItemId}
              editingItemId={editingItemId}
              onSelect={onSelect}
              onToggleExpand={onToggleExpand}
              onStartEdit={onStartEdit}
              onFinishEdit={onFinishEdit}
              onCancelEdit={onCancelEdit}
              onDelete={onDelete}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              enableDragDrop={enableDragDrop}
              maxDepth={maxDepth}
              allNodes={allNodes}
              renderChildren={renderChildren}
            />
          ))}
        </div>
      )}

      <ConfirmDialog
        isOpen={showDeleteDialog}
        title="Delete Item"
        message={`Are you sure you want to delete "${node.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        onConfirm={() => {
          onDelete(node.id);
          setShowDeleteDialog(false);
        }}
        onCancel={() => setShowDeleteDialog(false)}
      />
    </div>
  );
};
export default TreeItem;
