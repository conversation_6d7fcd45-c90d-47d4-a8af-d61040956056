import { TreeNodeI, FileI, FolderI } from "./types";

export const validateFileName = (
  name: string,
  existingNodes?: TreeNodeI[],
  currentNodeId?: string,
): { isValid: boolean; error?: string } => {
  if (!name.trim()) {
    return { isValid: false, error: "Name cannot be empty" };
  }

  if (name.length > 255) {
    return { isValid: false, error: "Name is too long (max 255 characters)" };
  }

  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: 'Name cannot contain < > : " / \\ | ? *' };
  }

  const reservedNames = [
    "CON",
    "PRN",
    "AUX",
    "NUL",
    "COM1",
    "COM2",
    "COM3",
    "COM4",
    "COM5",
    "COM6",
    "COM7",
    "COM8",
    "COM9",
    "LPT1",
    "LPT2",
    "LPT3",
    "LPT4",
    "LPT5",
    "LPT6",
    "LPT7",
    "LPT8",
    "LPT9",
  ];
  if (reservedNames.includes(name.toUpperCase())) {
    return { isValid: false, error: "This name is reserved by the system" };
  }

  // Check for duplicate names in the same folder
  if (existingNodes) {
    const duplicateNode = existingNodes.find(
      (node) =>
        node.name.toLowerCase() === name.toLowerCase() &&
        node.id !== currentNodeId,
    );
    if (duplicateNode) {
      return {
        isValid: false,
        error: "A file or folder with this name already exists",
      };
    }
  }

  return { isValid: true };
};

export const validateFolderName = (
  name: string,
  existingNodes?: TreeNodeI[],
  currentNodeId?: string,
): { isValid: boolean; error?: string } => {
  const fileValidation = validateFileName(name, existingNodes, currentNodeId);
  if (!fileValidation.isValid) {
    return fileValidation;
  }

  if (name.startsWith(".") || name.endsWith(".")) {
    return {
      isValid: false,
      error: "Folder name cannot start or end with a dot",
    };
  }

  return { isValid: true };
};

export const getSiblingNodes = (
  nodes: TreeNodeI[],
  nodeId: string,
): TreeNodeI[] => {
  const node = findNodeById(nodes, nodeId);
  if (!node) return [];

  if (node.parentId === null) {
    // Root level nodes
    return nodes.filter((n) => n.parentId === null);
  } else {
    // Child nodes - find parent and return its children
    const parent = findNodeById(nodes, node.parentId);
    return parent?.type === "folder" ? parent.children : [];
  }
};

export const findNodeById = (
  nodes: TreeNodeI[],
  id: string,
): TreeNodeI | null => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.type === "folder" && node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

export const findParentNode = (
  nodes: TreeNodeI[],
  childId: string,
): FolderI | null => {
  for (const node of nodes) {
    if (node.type === "folder" && node.children) {
      if (node.children.some((child) => child.id === childId)) {
        return node;
      }
      const found = findParentNode(node.children, childId);
      if (found) return found;
    }
  }
  return null;
};

export const addNodeToTree = (
  nodes: TreeNodeI[],
  newNode: TreeNodeI,
  parentId: string | null,
): TreeNodeI[] => {
  if (parentId === null) {
    return [...nodes, newNode];
  }

  return nodes.map((node) => {
    // If this is the parent folder, add the new node to its children
    if (node.id === parentId && node.type === "folder") {
      return {
        ...node,
        children: [...(node.children || []), newNode],
        count: node.count + 1,
      };
    }

    // Otherwise, if this is a folder with children, recursively search in children
    // Use else if to prevent processing the same node twice
    else if (
      node.type === "folder" &&
      node.children &&
      node.children.length > 0
    ) {
      const updatedChildren = addNodeToTree(node.children, newNode, parentId);
      // Only update if children actually changed (parent was found in subtree)
      if (updatedChildren !== node.children) {
        return {
          ...node,
          children: updatedChildren,
        };
      }
    }

    return node;
  });
};

export const removeNodeFromTree = (
  nodes: TreeNodeI[],
  nodeId: string,
): TreeNodeI[] => {
  return nodes
    .filter((node) => node.id !== nodeId)
    .map((node) => {
      if (node.type === "folder" && node.children) {
        const updatedChildren = removeNodeFromTree(node.children, nodeId);
        return {
          ...node,
          children: updatedChildren,
          count: updatedChildren.length,
        };
      }
      return node;
    });
};

export const updateNodeInTree = (
  nodes: TreeNodeI[],
  nodeId: string,
  updates: Partial<TreeNodeI>,
): TreeNodeI[] => {
  return nodes.map((node) => {
    if (node.id === nodeId) {
      return { ...node, ...updates } as TreeNodeI;
    }
    // Use else if to prevent processing the same node twice
    else if (node.type === "folder" && node.children) {
      return {
        ...node,
        children: updateNodeInTree(node.children, nodeId, updates),
      } as FolderI;
    }
    return node;
  });
};

export const moveNodeInTree = (
  nodes: TreeNodeI[],
  nodeId: string,
  newParentId: string | null,
  // newIndex?: number,
): TreeNodeI[] => {
  const nodeToMove = findNodeById(nodes, nodeId);
  if (!nodeToMove) return nodes;

  if (nodeToMove.type === "folder" && newParentId === nodeId) {
    return nodes;
  }

  if (
    nodeToMove.type === "folder" &&
    isDescendant(nodes, nodeId, newParentId)
  ) {
    return nodes;
  }

  const nodesWithoutMoved = removeNodeFromTree(nodes, nodeId);

  const updatedNode = { ...nodeToMove, parentId: newParentId };

  return addNodeToTree(nodesWithoutMoved, updatedNode, newParentId);
};

export const isDescendant = (
  nodes: TreeNodeI[],
  ancestorId: string,
  descendantId: string | null,
): boolean => {
  if (!descendantId) return false;

  const ancestor = findNodeById(nodes, ancestorId);
  if (!ancestor || ancestor.type !== "folder") return false;

  const checkChildren = (children: TreeNodeI[]): boolean => {
    for (const child of children) {
      if (child.id === descendantId) return true;
      if (child.type === "folder" && checkChildren(child.children)) return true;
    }
    return false;
  };

  return checkChildren(ancestor.children);
};

export const getNodePath = (nodes: TreeNodeI[], nodeId: string): string => {
  const buildPath = (
    currentNodes: TreeNodeI[],
    targetId: string,
    currentPath: string[],
  ): string[] | null => {
    for (const node of currentNodes) {
      const newPath = [...currentPath, node.name];
      if (node.id === targetId) {
        return newPath;
      }
      if (node.type === "folder" && node.children) {
        const found = buildPath(node.children, targetId, newPath);
        if (found) return found;
      }
    }
    return null;
  };

  const path = buildPath(nodes, nodeId, []);
  return path ? path.join("/") : "";
};

const generateUniqueName = (
  baseName: string,
  existingNodes: TreeNodeI[],
): string => {
  let counter = 1;
  let uniqueName = baseName;

  while (
    existingNodes.some(
      (node) => node.name.toLowerCase() === uniqueName.toLowerCase(),
    )
  ) {
    if (baseName.includes(".")) {
      const parts = baseName.split(".");
      const extension = parts.pop();
      const nameWithoutExt = parts.join(".");
      uniqueName = `${nameWithoutExt} (${counter}).${extension}`;
    } else {
      uniqueName = `${baseName} (${counter})`;
    }
    counter++;
  }

  return uniqueName;
};

export const createNewFile = (
  name: string,
  parentId: string | null,
  existingNodes?: TreeNodeI[],
): FileI => {
  const uniqueName = existingNodes
    ? generateUniqueName(name, existingNodes)
    : name;
  const extension = uniqueName.includes(".")
    ? uniqueName.split(".").pop() || ""
    : "";

  return {
    id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: uniqueName,
    type: "file",
    extension,
    size: 0,
    lastModified: new Date(),
    path: "",
    parentId,
  };
};

export const createNewFolder = (
  name: string,
  parentId: string | null,
  existingNodes?: TreeNodeI[],
): FolderI => {
  const uniqueName = existingNodes
    ? generateUniqueName(name, existingNodes)
    : name;

  return {
    id: `folder_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: uniqueName,
    type: "folder",
    path: "",
    parentId,
    children: [],
    isExpanded: false,
    count: 0,
  };
};

/**
 * Count the total number of folders and files in a tree structure
 */
export const countTreeItems = (
  nodes: TreeNodeI[],
): { folders: number; files: number; total: number } => {
  let folders = 0;
  let files = 0;

  const countRecursively = (nodeList: TreeNodeI[]) => {
    for (const node of nodeList) {
      if (node.type === "folder") {
        folders++;
        if (node.children && node.children.length > 0) {
          countRecursively(node.children);
        }
      } else if (node.type === "file") {
        files++;
      }
    }
  };

  countRecursively(nodes);

  return {
    folders,
    files,
    total: folders + files,
  };
};
