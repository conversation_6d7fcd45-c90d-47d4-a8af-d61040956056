import { useEffect, useRef, useState } from "react";
import { ChevronDownIcon } from "../../../../../../../public/assets/svgs/common";

const LibraryDropdown: React.FC<{
    currentLibraryType: string;
    onLibraryTypeToggle: () => void;
}> = ({ currentLibraryType, onLibraryTypeToggle }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleOutsideClick = (event: MouseEvent) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node)
            ) {
                setIsOpen(false);
            }
        };

        document.addEventListener("mousedown", handleOutsideClick);
        return () => document.removeEventListener("mousedown", handleOutsideClick);
    }, []);

    const handleToggle = () => {
        setIsOpen(!isOpen);
    };

    const handleOptionSelect = () => {
        onLibraryTypeToggle();
        setIsOpen(false);
    };

    return (
        <div ref={dropdownRef} className="relative">
            <button
                onClick={handleToggle}
                className="flex items-center gap-1 text-sm font-semibold capitalize hover:bg-gray-100 px-2 py-1 rounded transition-colors"
            >
                {currentLibraryType} library
                <ChevronDownIcon />
            </button>

            {isOpen && (
                <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]">
                    <button
                        onClick={handleOptionSelect}
                        className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg transition-colors"
                    >
                        {currentLibraryType === "public" ? "Private" : "Public"} library
                    </button>
                </div>
            )}
        </div>
    );
};

export default LibraryDropdown;
