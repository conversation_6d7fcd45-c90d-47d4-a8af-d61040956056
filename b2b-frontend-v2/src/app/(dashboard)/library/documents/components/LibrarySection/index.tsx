import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/utils/class-merge";
import { LibrarySectionProps } from "../FolderSidebar/types";
import {
  AddFileIcon,
  AddFolderIcon,
  ChevronDownIcon,
} from "../../../../../../../public/assets/svgs/common";
import Tooltip from "@/components/Tooltip";

const LibrarySection: React.FC<LibrarySectionProps> = ({
  isExpanded,
  onToggle,
  onAddFile,
  onAddFolder,
  addFileTooltip = "Create a new file",
  addFolderTooltip = "Create a new folder",
  children,
}) => {
  const pathname = usePathname();
  const isActive = pathname.startsWith("/library/documents");

  const handleLibraryClick = (e: React.MouseEvent) => {
    if (!isExpanded) {
      onToggle();
    }
    if ((e.target as HTMLElement).closest(".action-buttons")) {
      e.preventDefault();
      return;
    }
  };

  return (
    <div className="group h-full flex flex-col">
      <Link
        href="/library/documents"
        onClick={handleLibraryClick}
        className={cn(
          "flex items-center w-full transition-all overflow-hidden text-ellipsis duration-300 ease-in-out",
          "px-2 py-2 rounded-lg flex-shrink-0",
          isActive
            ? "bg-secondary-100 text-primary-600 font-medium transform scale-105"
            : "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900 hover:transform hover:scale-102",
          "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1",
          "group",
        )}
        aria-current={isActive ? "page" : undefined}
      >
        <div className="flex justify-between items-center gap-3 w-full">
          <div className="flex items-center">
            {/* Chevron */}
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onToggle();
              }}
              className={cn(
                "flex-shrink-0 transition-all duration-300 ease-in-out group-hover:scale-110 p-1",
                isExpanded && "rotate-180",
                isActive
                  ? "text-primary-600"
                  : "text-neutral-700 group-hover:text-neutral-900",
              )}
            >
              <ChevronDownIcon />
            </button>
            <span
              className={cn(
                "text-sm whitespace-nowrap transition-all duration-300 ease-in-out flex-1 text-left",
                isActive
                  ? "font-medium"
                  : "font-normal group-hover:font-medium",
              )}
            >
              Library
            </span>
          </div>

          <div className="flex items-center gap-1">
            <div className="action-buttons flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Tooltip content={addFileTooltip} position="bottom">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAddFile();
                  }}
                  className="p-1 rounded hover:bg-primary-200 transition-colors"
                >
                  <AddFileIcon />
                </button>
              </Tooltip>
              <Tooltip content={addFolderTooltip} position="bottom">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onAddFolder();
                }}
                className="p-1 rounded hover:bg-primary-200 transition-colors"
                aria-label={addFolderTooltip}
              >
                <AddFolderIcon />
              </button>
              </Tooltip>
            </div>
          </div>
        </div>
      </Link>

      {isExpanded && (
        <div className="ml-2 flex-1 min-h-0">
          <div className="pl-2 border-l border-transparent group-hover:border-neutral-200 transition-colors duration-200 h-full">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};
export default LibrarySection;
