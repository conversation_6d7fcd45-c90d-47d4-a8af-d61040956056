import React from "react";
import { SidebarFooterPropsI } from "./types";

const SidebarFooter = (props: SidebarFooterPropsI) => {
  const {
    sidebarCollapsed,
    isSplitView,
    currentLibraryType,
    sidebarWidth = 256,
    publicSidebarWidth = 256,
  } = props;
  return (
    <div
      className={`fixed bottom-0 z-30 bg-gray-50 border-t border-gray-200 px-4 py-1 ${
        isSplitView && currentLibraryType === "private" ? "" : "border-r"
      }`}
      style={{
        width: `${sidebarWidth}px`,
        left: (() => {
          const mainSidebarWidth = sidebarCollapsed ? 64 : 256; // Main sidebar width
          if (isSplitView) {
            // In split view, public sidebar is always at mainSidebarWidth
            // Private sidebar is at mainSidebarWidth + publicSidebarWidth
            return currentLibraryType === "public"
              ? `${mainSidebarWidth}px`
              : `${mainSidebarWidth + publicSidebarWidth}px`;
          } else {
            return `${mainSidebarWidth}px`;
          }
        })(),
      }}
    >
      <div className="flex items-center">
        <div className="text-xs text-gray-600 font-medium">Folders: 0</div>
      </div>
    </div>
  );
};

export default SidebarFooter;
