"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import {
  TreeNodeI,
  FolderI,
} from "../../documents/components/FolderSidebar/types";

type LibraryContextType = {
  // Folder/File Tree State
  fileTree: TreeNodeI[];
  setFileTree: (tree: TreeNodeI[]) => void;
  selectedItem: string | null;
  setSelectedItem: (itemId: string | null) => void;

  // Legacy folder support
  folders: FolderI[];
  setFolders: (folders: FolderI[]) => void;
  selectedFolder: string | null;
  setSelectedFolder: (folderId: string | null) => void;

  // Sidebar state
  isSidebarExpanded: boolean;
  setSidebarExpanded: (expanded: boolean) => void;

  // Actions
  onItemMove: (itemId: string, newParentId: string | null) => void;
  onItemCreate: (item: {
    type: "file" | "folder";
    name: string;
    parentId: string | null;
  }) => void;
  onItemRename: (itemId: string, newName: string) => void;
  onItemDelete: (itemId: string) => void;
};

const LibraryContext = createContext<LibraryContextType | undefined>(undefined);

export const useLibrary = () => {
  const context = useContext(LibraryContext);
  if (!context) {
    throw new Error("useLibrary must be used within a LibraryProvider");
  }
  return context;
};

export const LibraryProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // State management
  const [fileTree, setFileTree] = useState<TreeNodeI[]>([]);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [folders, setFolders] = useState<FolderI[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [isSidebarExpanded, setSidebarExpanded] = useState(false);

  // Actions
  const onItemMove = useCallback(
    (itemId: string, newParentId: string | null) => {
      // Implement move logic or call API
      console.log("Moving item:", itemId, "to parent:", newParentId);
      // TODO: Add API call here
    },
    [],
  );

  const onItemCreate = useCallback(
    (item: {
      type: "file" | "folder";
      name: string;
      parentId: string | null;
    }) => {
      // Implement create logic or call API
      console.log("Creating item:", item);
      // TODO: Add API call here
    },
    [],
  );

  const onItemRename = useCallback((itemId: string, newName: string) => {
    // Implement rename logic or call API
    console.log("Renaming item:", itemId, "to:", newName);
    // TODO: Add API call here
  }, []);

  const onItemDelete = useCallback((itemId: string) => {
    // Implement delete logic or call API
    console.log("Deleting item:", itemId);
    // TODO: Add API call here
  }, []);

  const value = {
    fileTree,
    setFileTree,
    selectedItem,
    setSelectedItem,
    folders,
    setFolders,
    selectedFolder,
    setSelectedFolder,
    isSidebarExpanded,
    setSidebarExpanded,
    onItemMove,
    onItemCreate,
    onItemRename,
    onItemDelete,
  };

  return (
    <LibraryContext.Provider value={value}>{children}</LibraryContext.Provider>
  );
};
