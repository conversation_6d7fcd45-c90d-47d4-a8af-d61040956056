"use client";

import React from "react";
import DashboardHeader from "@/components/DashboardHeader";
import { useLibrary } from "../components/LibraryLayoutClient/context";

const ApprovalsPage = () => {
  const { selectedFolder } = useLibrary();

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Approvals"
        description="Review and approve pending documents"
        actions={[
          {
            label: "Approve All",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Approve All"),
          },
          {
            label: "Review Queue",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Review Queue"),
          },
        ]}
      />

      <div className="flex-1 p-6 bg-neutral-50">
        <div className="text-center py-12">
          <p className="text-lg font-medium text-neutral-700">
            {selectedFolder
              ? `Approvals in ${selectedFolder}`
              : "Document Approvals"}
          </p>
          <p className="text-sm text-neutral-500 mt-2">
            Documents pending approval will appear here
          </p>
        </div>
      </div>
    </div>
  );
};

export default ApprovalsPage;
