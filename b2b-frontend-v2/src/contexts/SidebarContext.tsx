"use client";

import React, { createContext, useContext, ReactNode } from "react";

type SidebarContextType = {
  sidebarCollapsed: boolean;
};

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};

type SidebarProviderProps = {
  children: ReactNode;
  sidebarCollapsed: boolean;
};

export const SidebarProvider: React.FC<SidebarProviderProps> = ({
  children,
  sidebarCollapsed,
}) => {
  return (
    <SidebarContext.Provider value={{ sidebarCollapsed }}>
      {children}
    </SidebarContext.Provider>
  );
};
