"use client";

import { useState } from "react";
import { sidebarItems } from "@/constants/common/data";
import Sidebar from "@/components/Sidebar";
import StickyHeader from "@/components/StickyHeader";
import { SidebarProvider } from "@/contexts/SidebarContext";
import { DashboardLayoutPropsI } from "./types";

const DashboardLayout = ({ children }: DashboardLayoutPropsI) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const handleToggleSidebar = () => {
    setSidebarCollapsed((prev) => {
      const newCollapsed = !prev;
      if (newCollapsed) {
        setExpandedItems(new Set());
      }
      return newCollapsed;
    });
  };

  const handleToggleExpand = (itemHref: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(itemHref)) {
        newSet.delete(itemHref);
      } else {
        newSet.add(itemHref);
      }
      return newSet;
    });
  };

  const handleSearch = (query: string) => {
    console.log("Search query:", query);
    // Implement search functionality here
  };

  const handleNotificationClick = () => {
    console.log("Notification clicked");
    // Implement notification functionality here
  };

  const handleProfileClick = () => {
    console.log("Profile clicked");
    // Implement profile functionality here
  };

  return (
    <SidebarProvider sidebarCollapsed={sidebarCollapsed}>
      <main className="flex min-h-screen max-h-screen w-full overflow-hidden">
        <Sidebar
          items={sidebarItems}
          hasLogo
          level={1}
          collapsed={sidebarCollapsed}
          className="flex-shrink-0"
          onToggleSidebar={handleToggleSidebar}
          onToggleExpand={handleToggleExpand}
          expandedItems={expandedItems}
        />
        <div className="flex-1 flex flex-col overflow-hidden">
          <StickyHeader
            userName="Vipin"
            onSearch={handleSearch}
            onNotificationClick={handleNotificationClick}
            onProfileClick={handleProfileClick}
            sidebarCollapsed={sidebarCollapsed}
          />
          <div className="flex-1 overflow-auto pt-[3.45rem]">{children}</div>
        </div>
      </main>
    </SidebarProvider>
  );
};
export default DashboardLayout;
