export type ResizeHandlePropsI = {
    onMouseDown: (e: React.MouseEvent) => void;
    onDoubleClick: () => void;
    isResizing: boolean;
    className?: string;
};


export type SidebarType = "single" | "public" | "private";

export type UseResizableSidebarPropsI = {
    sidebarType: SidebarType;
    defaultWidth?: number;
    minWidth?: number;
    maxWidth?: number;
}

export type UseResizableSidebarReturnI = {
    width: number;
    isResizing: boolean;
    handleMouseDown: (e: React.MouseEvent) => void;
    resetWidth: () => void;
}