import React from "react";
import { ResizeHandlePropsI } from "./types";

const ResizeHandle: React.FC<ResizeHandlePropsI> = ({
  onMouseDown,
  onDoubleClick,
  isResizing,
  className = "",
}) => {
  return (
    <div
      className={`absolute top-0 right-0 bottom-0 w-1 cursor-col-resize group hover:bg-blue-500 transition-colors duration-150 ${
        isResizing ? "bg-blue-500" : "bg-transparent"
      } ${className}`}
      onMouseDown={onMouseDown}
      onDoubleClick={onDoubleClick}
      style={{
        zIndex: 50,
      }}
    >
      <div className="absolute inset-y-0 -left-1 -right-1 w-3" />

      <div
        className={`absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-150 ${
          isResizing ? "opacity-100" : ""
        }`}
      />
    </div>
  );
};

export default ResizeHandle;
