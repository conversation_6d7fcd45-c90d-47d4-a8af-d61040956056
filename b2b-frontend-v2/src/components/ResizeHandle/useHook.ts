import { useState, useEffect, useCallback, useRef } from "react";
import { UseResizableSidebarPropsI, UseResizableSidebarReturnI } from "./types";

const STORAGE_KEYS = {
  SINGLE_WIDTH: "folderSidebar_single_width",
  PUBLIC_WIDTH: "folderSidebar_public_width",
  PRIVATE_WIDTH: "folderSidebar_private_width",
} as const;

const DEFAULT_WIDTH = 256;
const MIN_WIDTH = 200;
const MAX_WIDTH = 600;

const useResizableSidebar = ({
  sidebarType,
  defaultWidth = DEFAULT_WIDTH,
  minWidth = MIN_WIDTH,
  maxWidth = MAX_WIDTH,
}: UseResizableSidebarPropsI): UseResizableSidebarReturnI => {
  const getStorageKey = useCallback(() => {
    switch (sidebarType) {
      case "single":
        return STORAGE_KEYS.SINGLE_WIDTH;
      case "public":
        return STORAGE_KEYS.PUBLIC_WIDTH;
      case "private":
        return STORAGE_KEYS.PRIVATE_WIDTH;
      default:
        return STORAGE_KEYS.SINGLE_WIDTH;
    }
  }, [sidebarType]);

  const getInitialWidth = useCallback(() => {
    const storageKey = getStorageKey();
    const savedWidth = localStorage.getItem(storageKey);
    if (savedWidth) {
      const parsedWidth = parseInt(savedWidth, 10);
      if (parsedWidth >= minWidth && parsedWidth <= maxWidth) {
        return parsedWidth;
      }
    }
    return defaultWidth;
  }, [minWidth, maxWidth, defaultWidth]);

  const [width, setWidth] = useState(getInitialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const saveWidthToStorage = useCallback((newWidth: number) => {
    const storageKey = getStorageKey();
    localStorage.setItem(storageKey, newWidth.toString());
  }, [getStorageKey]);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startXRef.current;
      const newWidth = startWidthRef.current + deltaX;

      const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
      setWidth(constrainedWidth);
    },
    [isResizing, minWidth, maxWidth],
  );

  const handleMouseUp = useCallback(() => {
    if (!isResizing) return;

    // Save the final width to localStorage when resizing ends
    saveWidthToStorage(width);
    setIsResizing(false);
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  }, [isResizing, width, saveWidthToStorage]);

  useEffect(() => {
    if (isResizing) {
      document.body.style.cursor = "col-resize";
      document.body.style.userSelect = "none";

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      startXRef.current = e.clientX;
      startWidthRef.current = width;
      setIsResizing(true);
    },
    [width],
  );

  const resetWidth = useCallback(() => {
    setWidth(defaultWidth);
    saveWidthToStorage(defaultWidth);
  }, [defaultWidth, saveWidthToStorage]);

  return {
    width,
    isResizing,
    handleMouseDown,
    resetWidth,
  };
};

export default useResizableSidebar;